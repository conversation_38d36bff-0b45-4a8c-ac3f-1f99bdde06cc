name: "no-anti-affinity"
description: "Indicates when deployments with multiple replicas fail to specify inter-pod anti-affinity, to ensure that the orchestrator attempts to schedule replicas on different nodes."
remediation: >-
  Specify anti-affinity in your pod specification to ensure that the orchestrator attempts to schedule replicas on different nodes.
  Using podAntiAffinity, specify a labelSelector that matches pods for the deployment,
  and set the topologyKey to kubernetes.io/hostname.
  Refer to https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity for details.
scope:
  objectKinds:
    - DeploymentLike
template: "anti-affinity"
params:
  minReplicas: 2
