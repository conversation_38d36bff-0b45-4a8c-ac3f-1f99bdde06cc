name: "wildcard-in-rules"
description: >-
  Indicate when a wildcard is used in Role or ClusterRole rules.
  CIS Benchmark 5.1.3 Use of wildcards is not optimal from a security perspective as it may allow for inadvertent access to be granted when new resources are added to the Kubernetes API either as CRDs or in later versions of the product.
remediation: "Where possible replace any use of wildcards in clusterroles and roles with specific objects or actions."
scope:
  objectKinds:
    - ClusterRole
    - Role
template: "wildcard-in-rules"
