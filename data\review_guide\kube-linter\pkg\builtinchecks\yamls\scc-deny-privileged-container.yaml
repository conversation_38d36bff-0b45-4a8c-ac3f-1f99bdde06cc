name: "scc-deny-privileged-container"
description: "Indicates when allowPrivilegedContainer SecurityContextConstraints set to true"
remediation: >-
  SecurityContextConstraints has AllowPrivilegedContainer set to "true". Using this option is dangerous, please consider using allowedCapabilities instead. Refer to https://docs.openshift.com/container-platform/4.12/authentication/managing-security-context-constraints.html#scc-settings_configuring-internal-oauth for details.
scope:
  objectKinds:
    - SecurityContextConstraints
template: "scc-deny-privileged-container"
params:
  AllowPrivilegedContainer: true