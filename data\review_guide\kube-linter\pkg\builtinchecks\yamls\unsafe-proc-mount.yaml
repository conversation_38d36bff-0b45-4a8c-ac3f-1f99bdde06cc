name: "unsafe-proc-mount"
description: "Alert on deployments with unsafe /proc mount (procMount=Unmasked) that will bypass the default masking behavior of the container runtime"
remediation: >-
  Ensure container does not unsafely exposes parts of /proc by setting procMount=Default. 
  Unmasked ProcMount bypasses the default masking behavior of the container runtime.
  See https://kubernetes.io/docs/concepts/security/pod-security-standards/ for more details.
scope:
  objectKinds:
    - DeploymentLike
template: "unsafe-proc-mount"
