name: "unsafe-sysctls"
description: "Alert on deployments specifying unsafe sysctls that may lead to severe problems like wrong behavior of containers"
remediation: >-
  Ensure container does not allow unsafe allocation of system resources by removing unsafe sysctls configurations.
  For more details see https://kubernetes.io/docs/tasks/administer-cluster/sysctl-cluster/
  https://docs.docker.com/engine/reference/commandline/run/#configure-namespaced-kernel-parameters-sysctls-at-runtime.
scope:
  objectKinds:
    - DeploymentLike
template: "unsafe-sysctls"
params:
  unsafeSysCtls: ["kernel.msg", "kernel.sem", "kernel.shm", "fs.mqueue.", "net."]
