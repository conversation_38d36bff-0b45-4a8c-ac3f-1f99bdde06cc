name: "read-secret-from-env-var"
description: >-
  Indicates when a deployment reads secret from environment variables.
  CIS Benchmark 5.4.1: "Prefer using secrets as files over secrets as environment variables. "
remediation: >-
  If possible, rewrite application code to read secrets from mounted secret files, rather than from environment variables.
  Refer to https://kubernetes.io/docs/concepts/configuration/secret/#using-secrets for details.
scope:
  objectKinds:
  - DeploymentLike
template: "read-secret-from-env-var"
