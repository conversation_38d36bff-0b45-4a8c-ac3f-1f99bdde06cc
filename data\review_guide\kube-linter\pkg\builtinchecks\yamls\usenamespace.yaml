name: "use-namespace"
description: >-
  Indicates when a resource is deployed to the default namespace.  
  CIS Benchmark 5.7.1: Create administrative boundaries between resources using namespaces.
  CIS Benchmark 5.7.4: The default namespace should not be used.
remediation: "Create namespaces for objects in your deployment."
scope:
  objectKinds:
    - DeploymentLike
    - Service
template: "use-namespace"
